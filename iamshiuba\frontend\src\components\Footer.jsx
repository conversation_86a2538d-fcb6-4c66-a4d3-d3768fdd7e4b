import { Link } from 'react-router-dom'

const Footer = () => {
  const currentYear = new Date().getFullYear()

  return (
    <footer className="bg-dark-50 dark:bg-dark-800 border-t border-dark-200 dark:border-dark-700">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {/* Brand */}
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <img 
                src="/static/img/is_web.svg" 
                alt="iamshiuba" 
                className="h-6 w-6"
              />
              <span className="text-lg font-bold text-gradient">
                iamshiuba
              </span>
            </div>
            <p className="text-dark-600 dark:text-dark-400 text-sm">
              Personal website and music platform showcasing creative works and updates.
            </p>
          </div>

          {/* Quick Links */}
          <div className="space-y-4">
            <h3 className="text-sm font-semibold text-dark-900 dark:text-dark-100 uppercase tracking-wider">
              Quick Links
            </h3>
            <div className="space-y-2">
              <Link 
                to="/streaming" 
                className="block text-dark-600 dark:text-dark-400 hover:text-primary-600 dark:hover:text-primary-400 text-sm transition-colors"
              >
                Streaming
              </Link>
              <Link 
                to="/about" 
                className="block text-dark-600 dark:text-dark-400 hover:text-primary-600 dark:hover:text-primary-400 text-sm transition-colors"
              >
                About
              </Link>
              <Link 
                to="/updates" 
                className="block text-dark-600 dark:text-dark-400 hover:text-primary-600 dark:hover:text-primary-400 text-sm transition-colors"
              >
                Updates
              </Link>
            </div>
          </div>

          {/* Legal */}
          <div className="space-y-4">
            <h3 className="text-sm font-semibold text-dark-900 dark:text-dark-100 uppercase tracking-wider">
              Legal
            </h3>
            <div className="space-y-2">
              <Link 
                to="/privacy" 
                className="block text-dark-600 dark:text-dark-400 hover:text-primary-600 dark:hover:text-primary-400 text-sm transition-colors"
              >
                Privacy Policy
              </Link>
              <Link 
                to="/terms" 
                className="block text-dark-600 dark:text-dark-400 hover:text-primary-600 dark:hover:text-primary-400 text-sm transition-colors"
              >
                Terms of Service
              </Link>
            </div>
          </div>
        </div>

        <div className="mt-8 pt-8 border-t border-dark-200 dark:border-dark-700">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            <p className="text-dark-600 dark:text-dark-400 text-sm">
              © {currentYear} iamshiuba. All rights reserved.
            </p>
            <div className="flex items-center space-x-4">
              <span className="text-dark-600 dark:text-dark-400 text-sm">
                Built with React & Flask
              </span>
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}

export default Footer
