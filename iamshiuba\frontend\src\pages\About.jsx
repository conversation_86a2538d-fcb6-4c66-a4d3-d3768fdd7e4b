const About = () => {
  return (
    <div className="min-h-screen bg-white dark:bg-dark-900">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="text-center mb-16">
          <h1 className="text-4xl md:text-5xl font-bold text-dark-900 dark:text-white mb-6">
            About <span className="text-gradient">iamshiuba</span>
          </h1>
          <p className="text-xl text-dark-600 dark:text-dark-400 max-w-2xl mx-auto">
            Learn more about my musical journey, creative process, and the story behind the music.
          </p>
        </div>

        <div className="space-y-12">
          <section className="card p-8">
            <h2 className="text-2xl font-bold text-dark-900 dark:text-white mb-4">
              My Story
            </h2>
            <div className="prose prose-lg dark:prose-invert max-w-none">
              <p className="text-dark-600 dark:text-dark-400 leading-relaxed">
                Welcome to my musical world. I'm iamshiuba, and this is where I share my passion for creating 
                and curating music that resonates with the soul. My journey began with a simple love for melodies 
                and has evolved into a platform where creativity meets technology.
              </p>
              <p className="text-dark-600 dark:text-dark-400 leading-relaxed">
                Through this platform, I aim to connect with fellow music enthusiasts, share my latest works, 
                and provide a space where music can be discovered, appreciated, and celebrated.
              </p>
            </div>
          </section>

          <section className="card p-8">
            <h2 className="text-2xl font-bold text-dark-900 dark:text-white mb-4">
              What I Do
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-3">
                <h3 className="text-lg font-semibold text-dark-900 dark:text-white">
                  Music Creation
                </h3>
                <p className="text-dark-600 dark:text-dark-400">
                  Crafting original compositions and curating playlists that tell stories and evoke emotions.
                </p>
              </div>
              <div className="space-y-3">
                <h3 className="text-lg font-semibold text-dark-900 dark:text-white">
                  Platform Development
                </h3>
                <p className="text-dark-600 dark:text-dark-400">
                  Building modern web experiences that showcase music in innovative and engaging ways.
                </p>
              </div>
              <div className="space-y-3">
                <h3 className="text-lg font-semibold text-dark-900 dark:text-white">
                  Community Building
                </h3>
                <p className="text-dark-600 dark:text-dark-400">
                  Connecting with music lovers and creating spaces for discovery and appreciation.
                </p>
              </div>
              <div className="space-y-3">
                <h3 className="text-lg font-semibold text-dark-900 dark:text-white">
                  Continuous Learning
                </h3>
                <p className="text-dark-600 dark:text-dark-400">
                  Always exploring new sounds, technologies, and ways to enhance the musical experience.
                </p>
              </div>
            </div>
          </section>

          <section className="card p-8">
            <h2 className="text-2xl font-bold text-dark-900 dark:text-white mb-4">
              Get in Touch
            </h2>
            <p className="text-dark-600 dark:text-dark-400 mb-6">
              I'd love to connect with fellow music enthusiasts, collaborators, and anyone who shares 
              a passion for creative expression.
            </p>
            <div className="flex flex-col sm:flex-row gap-4">
              <a 
                href="/streaming" 
                className="btn btn-primary"
              >
                Explore My Music
              </a>
              <a 
                href="/updates" 
                className="btn btn-secondary"
              >
                Follow Updates
              </a>
            </div>
          </section>
        </div>
      </div>
    </div>
  )
}

export default About
