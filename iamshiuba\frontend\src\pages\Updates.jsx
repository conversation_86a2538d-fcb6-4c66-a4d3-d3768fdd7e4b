import { useState, useEffect } from 'react'
import { updateService } from '../services/updateService'
import LoadingSpinner from '../components/LoadingSpinner'

const Updates = () => {
  const [updates, setUpdates] = useState([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchUpdates = async () => {
      try {
        const response = await updateService.getUpdatesByVersion('DESC')
        setUpdates(response.updates || [])
      } catch (error) {
        console.error('Error fetching updates:', error)
        setUpdates([])
      } finally {
        setLoading(false)
      }
    }

    fetchUpdates()
  }, [])

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-white dark:bg-dark-900">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="text-center mb-12">
          <h1 className="text-4xl md:text-5xl font-bold text-dark-900 dark:text-white mb-6">
            Latest <span className="text-gradient">Updates</span>
          </h1>
          <p className="text-xl text-dark-600 dark:text-dark-400 max-w-2xl mx-auto">
            Stay up to date with my latest releases, announcements, and project updates.
          </p>
        </div>

        {updates.length > 0 ? (
          <div className="space-y-8">
            {updates.map((update, index) => (
              <article key={update.id} className="card p-8 space-y-6">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <span className="px-4 py-2 bg-primary-100 dark:bg-primary-900/30 text-primary-800 dark:text-primary-200 rounded-full text-sm font-medium">
                      Version {update.version}
                    </span>
                    {index === 0 && (
                      <span className="px-3 py-1 bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-200 rounded-full text-xs font-medium">
                        Latest
                      </span>
                    )}
                  </div>
                  <time className="text-sm text-dark-500 dark:text-dark-400">
                    {new Date(update.created_at).toLocaleDateString('en-US', {
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric'
                    })}
                  </time>
                </div>

                <div className="space-y-4">
                  <h2 className="text-2xl font-bold text-dark-900 dark:text-white">
                    {update.title}
                  </h2>
                  
                  <div className="prose prose-lg dark:prose-invert max-w-none">
                    <p className="text-dark-600 dark:text-dark-400 leading-relaxed whitespace-pre-wrap">
                      {update.content}
                    </p>
                  </div>

                  {update.preview_link && (
                    <div className="pt-4">
                      <a
                        href={update.preview_link}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="btn btn-primary inline-flex items-center space-x-2"
                      >
                        <span>Preview</span>
                        <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                        </svg>
                      </a>
                    </div>
                  )}
                </div>
              </article>
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <div className="space-y-4">
              <svg className="w-16 h-16 mx-auto text-dark-400 dark:text-dark-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z" />
              </svg>
              <h3 className="text-lg font-medium text-dark-900 dark:text-white">
                No updates yet
              </h3>
              <p className="text-dark-600 dark:text-dark-400">
                Check back later for the latest news and updates.
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default Updates
