from datetime import datetime
from flask import (
    Flask,
    send_from_directory,
    request,
    jsonify,
)
from flask_compress import Compress
from flask_limiter import Limiter
from flask_limiter.util import get_remote_address
from flask_cors import CORS
from flask_sqlalchemy import SQLAlchemy
from flask_migrate import Migrate
from flask_bcrypt import B<PERSON>rypt
from werkzeug.middleware.proxy_fix import ProxyFix
from werkzeug.middleware.shared_data import SharedDataMiddleware
import os
import logging
from config import Config
from database import init_db

# Import new modules - with error handling for missing dependencies
try:
    from auth.models import db, bcrypt
    MODERN_FEATURES_AVAILABLE = True
except ImportError as e:
    print(f"Warning: Modern features not available due to missing dependencies: {e}")
    print("Please install requirements: pip install -r requirements.txt")
    MODERN_FEATURES_AVAILABLE = False
    # Create fallback objects
    db = None
    bcrypt = None


def create_app(config_class=Config):
    app = Flask(
        __name__,
        static_url_path="/static",
        static_folder=os.path.abspath("static"),
    )
    app.config.from_object(config_class)

    # Initialize extensions
    if MODERN_FEATURES_AVAILABLE:
        db.init_app(app)
        bcrypt.init_app(app)
        migrate = Migrate(app, db)
        cors = CORS(app, origins=config_class.ALLOWED_ORIGINS)
    else:
        # Fallback CORS for basic functionality
        cors = CORS(app, origins=config_class.ALLOWED_ORIGINS)

    Compress(app)

    # Configure static files for production
    app.wsgi_app = SharedDataMiddleware(
        app.wsgi_app,
        {"/static": os.path.join(os.path.dirname(os.path.abspath(__file__)), "static")},
    )

    # Configure limiter
    limiter = Limiter(
        app=app,
        key_func=get_remote_address,
        storage_uri="memory://",
        storage_options={"socket_connect_timeout": 30},
        default_limits=["200 per day", "50 per hour"],
    )

    # Configure logging
    if not app.debug:
        app.logger.setLevel(logging.INFO)
        app.logger.info("Application startup")

    # Handle proxy headers
    app.wsgi_app = ProxyFix(app.wsgi_app, x_for=1, x_proto=1)

    # Security Headers
    @app.after_request
    def add_security_headers(response):
        response.headers.update(
            {
                "X-Content-Type-Options": "nosniff",
                "X-Frame-Options": "SAMEORIGIN",
                "X-XSS-Protection": "1; mode=block",
                "Content-Security-Policy": "default-src 'self' https:; img-src 'self' https: data:; style-src 'self' https: 'unsafe-inline'; script-src 'self' https: 'unsafe-inline' 'unsafe-eval'",
                "Strict-Transport-Security": "max-age=31536000; includeSubDomains",
                "Referrer-Policy": "strict-origin-when-cross-origin",
            }
        )
        return response

    # CORS Headers
    @app.after_request
    def add_cors_headers(response):
        allowed_origins = [
            ".vercel.app",
            ".fly.dev",
            "localhost:5000",
            "127.0.0.1:5000",
        ]
        if request.headers.get("Origin") in allowed_origins:
            response.headers["Access-Control-Allow-Origin"] = request.headers["Origin"]
            response.headers["Access-Control-Allow-Methods"] = "GET, POST, OPTIONS"
            response.headers["Access-Control-Allow-Headers"] = "Content-Type"
        return response

    # Maintenance mode
    @app.before_request
    def check_maintenance():
        if app.config.get("MAINTENANCE_MODE", False) and request.path != "/maintenance":
            return jsonify({"error": "Service temporarily unavailable"}), 503

    # Error Handlers for API
    @app.errorhandler(404)
    def page_not_found(e):
        app.logger.info(f"404 error: {request.path} - {request.remote_addr}")
        return jsonify({"error": "Not found"}), 404

    @app.errorhandler(500)
    def internal_server_error(e):
        app.logger.error(
            f"500 error: {str(e)} - {request.path} - {request.remote_addr}"
        )
        return jsonify({"error": "Internal server error"}), 500

    @app.errorhandler(403)
    def forbidden(e):
        app.logger.warning(f"403 error: {request.path} - {request.remote_addr}")
        return jsonify({"error": "Forbidden"}), 403

    @app.errorhandler(429)
    def too_many_requests(e):
        app.logger.warning(f"429 error: {request.path} - {request.remote_addr}")
        return jsonify({"error": "Too many requests"}), 429

    # Static file routes
    @app.route("/favicon.ico")
    def favicon():
        return send_from_directory(
            os.path.join(app.root_path, "static/img"),
            "is_web.svg",
            mimetype="image/svg+xml",
        )

    @app.route("/health")
    def health():
        return {"status": "healthy"}, 200

    # Serve React app for all non-API routes
    @app.route('/', defaults={'path': ''})
    @app.route('/<path:path>')
    def serve_react_app(path):
        # Serve static files directly
        if path.startswith('static/'):
            return send_from_directory(app.root_path, path)

        # For all other routes, serve the React app
        return send_from_directory(
            os.path.join(app.root_path, 'static/dist'),
            'index.html'
        )

    # Register API blueprints
    from api.v1 import api_v1
    from auth.routes import auth_bp

    app.register_blueprint(api_v1, url_prefix='/api/v1')
    app.register_blueprint(auth_bp, url_prefix='/auth')

    # Legacy API routes (to be removed after frontend migration)
    @app.route("/api/playlists")
    def legacy_api_playlists():
        from playlists.database import get_all_playlists

        platform = request.args.get("platform")
        playlists = get_all_playlists()
        if platform:
            playlists = [p for p in playlists if p.get("platform") == platform]
        return jsonify({"playlists": playlists})

    # Register blueprints (only if modern features are available)
    if MODERN_FEATURES_AVAILABLE:
        print("✅ Modern authentication and API features enabled")
    else:
        print("⚠️  Running in legacy mode - install requirements for modern features")

    return app


# Create app instance
app = create_app()

# Initialize databases
init_db()

if __name__ == "__main__":
    app.run()
