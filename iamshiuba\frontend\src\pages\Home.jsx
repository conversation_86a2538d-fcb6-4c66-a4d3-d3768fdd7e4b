import { useState, useEffect } from 'react'
import { Link } from 'react-router-dom'
import { playlistService } from '../services/playlistService'
import { updateService } from '../services/updateService'
import LoadingSpinner from '../components/LoadingSpinner'

const Home = () => {
  const [highlightPlaylist, setHighlightPlaylist] = useState(null)
  const [recentUpdates, setRecentUpdates] = useState([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchData = async () => {
      try {
        const [playlistResponse, updatesResponse] = await Promise.all([
          playlistService.getHighlightPlaylist().catch(() => ({ highlight: null })),
          updateService.getUpdates({ limit: 3, order: 'DESC' }).catch(() => ({ updates: [] }))
        ])

        setHighlightPlaylist(playlistResponse.highlight)
        setRecentUpdates(updatesResponse.updates || [])
      } catch (error) {
        console.error('Error fetching home data:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchData()
  }, [])

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-primary-600 via-purple-600 to-pink-600 text-white">
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24">
          <div className="text-center space-y-8">
            <div className="space-y-4">
              <h1 className="text-4xl md:text-6xl font-bold animate-fade-in">
                Welcome to <span className="text-gradient">iamshiuba</span>
              </h1>
              <p className="text-xl md:text-2xl text-white/90 max-w-3xl mx-auto animate-fade-in">
                Discover my musical journey, latest updates, and creative works
              </p>
            </div>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center animate-slide-up">
              <Link 
                to="/streaming" 
                className="btn bg-white text-primary-600 hover:bg-white/90 px-8 py-3 text-lg font-semibold"
              >
                Explore Music
              </Link>
              <Link 
                to="/about" 
                className="btn bg-white/10 text-white border border-white/30 hover:bg-white/20 px-8 py-3 text-lg font-semibold"
              >
                Learn More
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Highlight Playlist Section */}
      {highlightPlaylist && (
        <section className="py-16 bg-white dark:bg-dark-900">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-dark-900 dark:text-white mb-4">
                Featured Playlist
              </h2>
              <p className="text-lg text-dark-600 dark:text-dark-400">
                Check out my latest highlighted playlist
              </p>
            </div>
            
            <div className="max-w-2xl mx-auto">
              <div className="card p-8 text-center space-y-6">
                <h3 className="text-2xl font-bold text-dark-900 dark:text-white">
                  {highlightPlaylist.title}
                </h3>
                {highlightPlaylist.description && (
                  <p className="text-dark-600 dark:text-dark-400">
                    {highlightPlaylist.description}
                  </p>
                )}
                <div className="flex justify-center space-x-4">
                  <span className="px-3 py-1 bg-primary-100 dark:bg-primary-900/30 text-primary-800 dark:text-primary-200 rounded-full text-sm font-medium">
                    {highlightPlaylist.platform_name}
                  </span>
                  {highlightPlaylist.release_date && (
                    <span className="px-3 py-1 bg-dark-100 dark:bg-dark-700 text-dark-700 dark:text-dark-300 rounded-full text-sm">
                      {new Date(highlightPlaylist.release_date).toLocaleDateString()}
                    </span>
                  )}
                </div>
                <a
                  href={highlightPlaylist.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="btn btn-primary inline-flex items-center space-x-2"
                >
                  <span>Listen Now</span>
                  <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                  </svg>
                </a>
              </div>
            </div>
          </div>
        </section>
      )}

      {/* Recent Updates Section */}
      {recentUpdates.length > 0 && (
        <section className="py-16 bg-dark-50 dark:bg-dark-800">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-dark-900 dark:text-white mb-4">
                Latest Updates
              </h2>
              <p className="text-lg text-dark-600 dark:text-dark-400">
                Stay up to date with my latest news and releases
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {recentUpdates.map((update) => (
                <div key={update.id} className="card p-6 space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="px-3 py-1 bg-primary-100 dark:bg-primary-900/30 text-primary-800 dark:text-primary-200 rounded-full text-sm font-medium">
                      v{update.version}
                    </span>
                    <span className="text-sm text-dark-500 dark:text-dark-400">
                      {new Date(update.created_at).toLocaleDateString()}
                    </span>
                  </div>
                  <h3 className="text-xl font-semibold text-dark-900 dark:text-white">
                    {update.title}
                  </h3>
                  <p className="text-dark-600 dark:text-dark-400 line-clamp-3">
                    {update.content}
                  </p>
                  {update.preview_link && (
                    <a
                      href={update.preview_link}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-primary-600 dark:text-primary-400 hover:underline text-sm font-medium"
                    >
                      Preview →
                    </a>
                  )}
                </div>
              ))}
            </div>
            
            <div className="text-center mt-12">
              <Link to="/updates" className="btn btn-primary">
                View All Updates
              </Link>
            </div>
          </div>
        </section>
      )}

      {/* CTA Section */}
      <section className="py-16 bg-white dark:bg-dark-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="space-y-8">
            <h2 className="text-3xl md:text-4xl font-bold text-dark-900 dark:text-white">
              Ready to explore?
            </h2>
            <p className="text-lg text-dark-600 dark:text-dark-400 max-w-2xl mx-auto">
              Dive into my music collection, learn about my journey, and stay updated with the latest releases.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link to="/streaming" className="btn btn-primary px-8 py-3 text-lg">
                Browse Music
              </Link>
              <Link to="/about" className="btn btn-secondary px-8 py-3 text-lg">
                About Me
              </Link>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}

export default Home
