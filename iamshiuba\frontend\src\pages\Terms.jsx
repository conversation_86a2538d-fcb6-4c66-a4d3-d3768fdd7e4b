const Terms = () => {
  return (
    <div className="min-h-screen bg-white dark:bg-dark-900">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="text-center mb-12">
          <h1 className="text-4xl md:text-5xl font-bold text-dark-900 dark:text-white mb-6">
            Terms of Service
          </h1>
          <p className="text-lg text-dark-600 dark:text-dark-400">
            Last updated: {new Date().toLocaleDateString()}
          </p>
        </div>

        <div className="card p-8 space-y-8">
          <section className="space-y-4">
            <h2 className="text-2xl font-bold text-dark-900 dark:text-white">
              Acceptance of Terms
            </h2>
            <p className="text-dark-600 dark:text-dark-400 leading-relaxed">
              By accessing and using this website, you accept and agree to be bound by the terms 
              and provision of this agreement. If you do not agree to abide by the above, 
              please do not use this service.
            </p>
          </section>

          <section className="space-y-4">
            <h2 className="text-2xl font-bold text-dark-900 dark:text-white">
              Use License
            </h2>
            <p className="text-dark-600 dark:text-dark-400 leading-relaxed">
              Permission is granted to temporarily download one copy of the materials on this website 
              for personal, non-commercial transitory viewing only. This is the grant of a license, 
              not a transfer of title, and under this license you may not modify or copy the materials.
            </p>
          </section>

          <section className="space-y-4">
            <h2 className="text-2xl font-bold text-dark-900 dark:text-white">
              Disclaimer
            </h2>
            <p className="text-dark-600 dark:text-dark-400 leading-relaxed">
              The materials on this website are provided on an 'as is' basis. We make no warranties, 
              expressed or implied, and hereby disclaim and negate all other warranties including 
              without limitation, implied warranties or conditions of merchantability, fitness for 
              a particular purpose, or non-infringement of intellectual property or other violation of rights.
            </p>
          </section>

          <section className="space-y-4">
            <h2 className="text-2xl font-bold text-dark-900 dark:text-white">
              Limitations
            </h2>
            <p className="text-dark-600 dark:text-dark-400 leading-relaxed">
              In no event shall iamshiuba or its suppliers be liable for any damages (including, 
              without limitation, damages for loss of data or profit, or due to business interruption) 
              arising out of the use or inability to use the materials on this website.
            </p>
          </section>

          <section className="space-y-4">
            <h2 className="text-2xl font-bold text-dark-900 dark:text-white">
              User Accounts
            </h2>
            <p className="text-dark-600 dark:text-dark-400 leading-relaxed">
              When you create an account with us, you must provide information that is accurate, 
              complete, and current at all times. You are responsible for safeguarding the password 
              and for all activities that occur under your account.
            </p>
          </section>

          <section className="space-y-4">
            <h2 className="text-2xl font-bold text-dark-900 dark:text-white">
              Prohibited Uses
            </h2>
            <p className="text-dark-600 dark:text-dark-400 leading-relaxed">
              You may not use our service for any illegal or unauthorized purpose nor may you, 
              in the use of the service, violate any laws in your jurisdiction including but not 
              limited to copyright laws.
            </p>
          </section>

          <section className="space-y-4">
            <h2 className="text-2xl font-bold text-dark-900 dark:text-white">
              Revisions and Errata
            </h2>
            <p className="text-dark-600 dark:text-dark-400 leading-relaxed">
              The materials appearing on this website could include technical, typographical, 
              or photographic errors. We do not warrant that any of the materials on its website 
              are accurate, complete, or current.
            </p>
          </section>

          <section className="space-y-4">
            <h2 className="text-2xl font-bold text-dark-900 dark:text-white">
              Governing Law
            </h2>
            <p className="text-dark-600 dark:text-dark-400 leading-relaxed">
              These terms and conditions are governed by and construed in accordance with the laws 
              and you irrevocably submit to the exclusive jurisdiction of the courts in that state or location.
            </p>
          </section>
        </div>
      </div>
    </div>
  )
}

export default Terms
