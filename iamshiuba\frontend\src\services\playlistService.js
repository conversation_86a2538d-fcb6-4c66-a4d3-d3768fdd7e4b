import api from './api'

export const playlistService = {
  // Get all playlists with optional filters
  getPlaylists: async (filters = {}) => {
    const params = new URLSearchParams()
    
    if (filters.platform) params.append('platform', filters.platform)
    if (filters.tag) params.append('tag', filters.tag)
    if (filters.search) params.append('search', filters.search)
    
    const response = await api.get(`/api/v1/playlists?${params}`)
    return response.data
  },

  // Get playlists by platform
  getPlaylistsByPlatform: async (platform) => {
    const response = await api.get(`/api/v1/playlists/${platform}`)
    return response.data
  },

  // Get highlight playlist
  getHighlightPlaylist: async () => {
    const response = await api.get('/api/v1/playlists/highlight')
    return response.data
  },

  // Get single playlist
  getPlaylist: async (id) => {
    const response = await api.get(`/api/v1/playlists/${id}`)
    return response.data
  },

  // Admin functions
  createPlaylist: async (playlistData) => {
    const response = await api.post('/api/v1/playlists', playlistData)
    return response.data
  },

  updatePlaylist: async (id, playlistData) => {
    const response = await api.put(`/api/v1/playlists/${id}`, playlistData)
    return response.data
  },

  deletePlaylist: async (id) => {
    const response = await api.delete(`/api/v1/playlists/${id}`)
    return response.data
  },

  // Get available platforms
  getPlatforms: async () => {
    const response = await api.get('/api/v1/playlists/platforms')
    return response.data
  },

  // Get available tags
  getTags: async () => {
    const response = await api.get('/api/v1/playlists/tags')
    return response.data
  }
}
