{"name": "iamshiuba-frontend", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "test": "vitest"}, "dependencies": {"react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^7.6.0", "axios": "^1.9.0", "jwt-decode": "^4.0.0"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "vite": "^6.3.5", "vitest": "^2.1.8", "autoprefixer": "^10.4.20", "postcss": "^8.5.0", "tailwindcss": "^4.0.17"}}