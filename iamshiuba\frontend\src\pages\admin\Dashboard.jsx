import { useState, useEffect } from 'react'
import { Routes, Route, Link, useLocation } from 'react-router-dom'
import { updateService } from '../../services/updateService'
import { playlistService } from '../../services/playlistService'
import LoadingSpinner from '../../components/LoadingSpinner'

const AdminDashboard = () => {
  const [stats, setStats] = useState({
    totalUpdates: 0,
    totalPlaylists: 0,
    highlightPlaylists: 0
  })
  const [loading, setLoading] = useState(true)
  const location = useLocation()

  useEffect(() => {
    const fetchStats = async () => {
      try {
        const [updatesResponse, playlistsResponse] = await Promise.all([
          updateService.getUpdates().catch(() => ({ updates: [] })),
          playlistService.getPlaylists().catch(() => ({ playlists: [] }))
        ])

        const updates = updatesResponse.updates || []
        const playlists = playlistsResponse.playlists || []
        const highlights = playlists.filter(p => p.is_highlight).length

        setStats({
          totalUpdates: updates.length,
          totalPlaylists: playlists.length,
          highlightPlaylists: highlights
        })
      } catch (error) {
        console.error('Error fetching admin stats:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchStats()
  }, [])

  const navigation = [
    { name: 'Dashboard', href: '/admin', icon: '📊' },
    { name: 'Updates', href: '/admin/updates', icon: '📝' },
    { name: 'Playlists', href: '/admin/playlists', icon: '🎵' },
    { name: 'Settings', href: '/admin/settings', icon: '⚙️' }
  ]

  const isActive = (path) => location.pathname === path

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-dark-50 dark:bg-dark-900">
      <div className="flex">
        {/* Sidebar */}
        <div className="w-64 bg-white dark:bg-dark-800 shadow-lg">
          <div className="p-6">
            <Link to="/" className="flex items-center space-x-2">
              <img 
                src="/static/img/is_web.svg" 
                alt="iamshiuba" 
                className="h-8 w-8"
              />
              <span className="text-xl font-bold text-gradient">
                Admin Panel
              </span>
            </Link>
          </div>
          
          <nav className="mt-6">
            {navigation.map((item) => (
              <Link
                key={item.name}
                to={item.href}
                className={`flex items-center px-6 py-3 text-sm font-medium transition-colors ${
                  isActive(item.href)
                    ? 'bg-primary-50 dark:bg-primary-900/20 text-primary-600 dark:text-primary-400 border-r-2 border-primary-600'
                    : 'text-dark-700 dark:text-dark-300 hover:bg-dark-50 dark:hover:bg-dark-700'
                }`}
              >
                <span className="mr-3">{item.icon}</span>
                {item.name}
              </Link>
            ))}
          </nav>
        </div>

        {/* Main Content */}
        <div className="flex-1 p-8">
          <Routes>
            <Route path="/" element={
              <div className="space-y-8">
                <div>
                  <h1 className="text-3xl font-bold text-dark-900 dark:text-white">
                    Admin Dashboard
                  </h1>
                  <p className="text-dark-600 dark:text-dark-400 mt-2">
                    Manage your content and monitor site activity
                  </p>
                </div>

                {/* Stats Cards */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div className="card p-6">
                    <div className="flex items-center">
                      <div className="p-3 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
                        <span className="text-2xl">📝</span>
                      </div>
                      <div className="ml-4">
                        <p className="text-sm font-medium text-dark-600 dark:text-dark-400">
                          Total Updates
                        </p>
                        <p className="text-2xl font-bold text-dark-900 dark:text-white">
                          {stats.totalUpdates}
                        </p>
                      </div>
                    </div>
                  </div>

                  <div className="card p-6">
                    <div className="flex items-center">
                      <div className="p-3 bg-green-100 dark:bg-green-900/30 rounded-lg">
                        <span className="text-2xl">🎵</span>
                      </div>
                      <div className="ml-4">
                        <p className="text-sm font-medium text-dark-600 dark:text-dark-400">
                          Total Playlists
                        </p>
                        <p className="text-2xl font-bold text-dark-900 dark:text-white">
                          {stats.totalPlaylists}
                        </p>
                      </div>
                    </div>
                  </div>

                  <div className="card p-6">
                    <div className="flex items-center">
                      <div className="p-3 bg-yellow-100 dark:bg-yellow-900/30 rounded-lg">
                        <span className="text-2xl">⭐</span>
                      </div>
                      <div className="ml-4">
                        <p className="text-sm font-medium text-dark-600 dark:text-dark-400">
                          Featured Playlists
                        </p>
                        <p className="text-2xl font-bold text-dark-900 dark:text-white">
                          {stats.highlightPlaylists}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Quick Actions */}
                <div className="card p-6">
                  <h2 className="text-xl font-bold text-dark-900 dark:text-white mb-4">
                    Quick Actions
                  </h2>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <Link 
                      to="/admin/updates/new" 
                      className="btn btn-primary flex items-center justify-center space-x-2"
                    >
                      <span>📝</span>
                      <span>Create New Update</span>
                    </Link>
                    <Link 
                      to="/admin/playlists/new" 
                      className="btn btn-secondary flex items-center justify-center space-x-2"
                    >
                      <span>🎵</span>
                      <span>Add New Playlist</span>
                    </Link>
                  </div>
                </div>
              </div>
            } />
            <Route path="/updates" element={
              <div className="text-center py-12">
                <h2 className="text-2xl font-bold text-dark-900 dark:text-white mb-4">
                  Updates Management
                </h2>
                <p className="text-dark-600 dark:text-dark-400">
                  Updates management interface coming soon...
                </p>
              </div>
            } />
            <Route path="/playlists" element={
              <div className="text-center py-12">
                <h2 className="text-2xl font-bold text-dark-900 dark:text-white mb-4">
                  Playlists Management
                </h2>
                <p className="text-dark-600 dark:text-dark-400">
                  Playlists management interface coming soon...
                </p>
              </div>
            } />
            <Route path="/settings" element={
              <div className="text-center py-12">
                <h2 className="text-2xl font-bold text-dark-900 dark:text-white mb-4">
                  Settings
                </h2>
                <p className="text-dark-600 dark:text-dark-400">
                  Settings interface coming soon...
                </p>
              </div>
            } />
          </Routes>
        </div>
      </div>
    </div>
  )
}

export default AdminDashboard
