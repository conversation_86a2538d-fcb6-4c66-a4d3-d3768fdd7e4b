import { Link } from 'react-router-dom'

const NotFound = () => {
  return (
    <div className="min-h-screen flex items-center justify-center bg-dark-50 dark:bg-dark-900">
      <div className="text-center space-y-8 px-4">
        <div className="space-y-4">
          <h1 className="text-9xl font-bold text-primary-600 dark:text-primary-400">
            404
          </h1>
          <h2 className="text-3xl md:text-4xl font-bold text-dark-900 dark:text-white">
            Page Not Found
          </h2>
          <p className="text-lg text-dark-600 dark:text-dark-400 max-w-md mx-auto">
            The page you're looking for doesn't exist or has been moved.
          </p>
        </div>
        
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Link 
            to="/" 
            className="btn btn-primary px-8 py-3 text-lg"
          >
            Go Home
          </Link>
          <button 
            onClick={() => window.history.back()} 
            className="btn btn-secondary px-8 py-3 text-lg"
          >
            Go Back
          </button>
        </div>
        
        <div className="pt-8">
          <img 
            src="/static/img/is_web.svg" 
            alt="iamshiuba" 
            className="h-16 w-16 mx-auto opacity-50"
          />
        </div>
      </div>
    </div>
  )
}

export default NotFound
