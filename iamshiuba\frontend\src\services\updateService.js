import api from './api'

export const updateService = {
  // Get all updates
  getUpdates: async (params = {}) => {
    const queryParams = new URLSearchParams()
    
    if (params.limit) queryParams.append('limit', params.limit)
    if (params.offset) queryParams.append('offset', params.offset)
    if (params.order) queryParams.append('order', params.order)
    
    const response = await api.get(`/api/v1/updates?${queryParams}`)
    return response.data
  },

  // Get single update
  getUpdate: async (id) => {
    const response = await api.get(`/api/v1/updates/${id}`)
    return response.data
  },

  // Get updates by version
  getUpdatesByVersion: async (order = 'DESC') => {
    const response = await api.get(`/api/v1/updates/by-version?order=${order}`)
    return response.data
  },

  // Admin functions
  createUpdate: async (updateData) => {
    const response = await api.post('/api/v1/updates', updateData)
    return response.data
  },

  updateUpdate: async (id, updateData) => {
    const response = await api.put(`/api/v1/updates/${id}`, updateData)
    return response.data
  },

  deleteUpdate: async (id) => {
    const response = await api.delete(`/api/v1/updates/${id}`)
    return response.data
  }
}
