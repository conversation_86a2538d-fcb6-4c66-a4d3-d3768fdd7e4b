import { useState, useEffect } from 'react'
import { playlistService } from '../services/playlistService'
import LoadingSpinner from '../components/LoadingSpinner'

const Streaming = () => {
  const [playlists, setPlaylists] = useState([])
  const [loading, setLoading] = useState(true)
  const [selectedPlatform, setSelectedPlatform] = useState('all')
  const [selectedTag, setSelectedTag] = useState('all')

  useEffect(() => {
    const fetchPlaylists = async () => {
      try {
        const filters = {}
        if (selectedPlatform !== 'all') filters.platform = selectedPlatform
        if (selectedTag !== 'all') filters.tag = selectedTag

        const response = await playlistService.getPlaylists(filters)
        setPlaylists(response.playlists || [])
      } catch (error) {
        console.error('Error fetching playlists:', error)
        setPlaylists([])
      } finally {
        setLoading(false)
      }
    }

    fetchPlaylists()
  }, [selectedPlatform, selectedTag])

  const platforms = ['all', 'youtube', 'spotify', 'soundcloud']
  const tags = ['all', 'chill', 'electronic', 'ambient', 'experimental']

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-white dark:bg-dark-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="text-center mb-12">
          <h1 className="text-4xl md:text-5xl font-bold text-dark-900 dark:text-white mb-6">
            Music <span className="text-gradient">Streaming</span>
          </h1>
          <p className="text-xl text-dark-600 dark:text-dark-400 max-w-2xl mx-auto">
            Discover my curated playlists across different platforms and genres.
          </p>
        </div>

        {/* Filters */}
        <div className="mb-8 flex flex-col sm:flex-row gap-4 justify-center">
          <div className="space-y-2">
            <label className="block text-sm font-medium text-dark-700 dark:text-dark-300">
              Platform
            </label>
            <select
              value={selectedPlatform}
              onChange={(e) => setSelectedPlatform(e.target.value)}
              className="input"
            >
              {platforms.map(platform => (
                <option key={platform} value={platform}>
                  {platform === 'all' ? 'All Platforms' : platform.charAt(0).toUpperCase() + platform.slice(1)}
                </option>
              ))}
            </select>
          </div>
          
          <div className="space-y-2">
            <label className="block text-sm font-medium text-dark-700 dark:text-dark-300">
              Genre/Tag
            </label>
            <select
              value={selectedTag}
              onChange={(e) => setSelectedTag(e.target.value)}
              className="input"
            >
              {tags.map(tag => (
                <option key={tag} value={tag}>
                  {tag === 'all' ? 'All Tags' : tag.charAt(0).toUpperCase() + tag.slice(1)}
                </option>
              ))}
            </select>
          </div>
        </div>

        {/* Playlists Grid */}
        {playlists.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {playlists.map((playlist) => (
              <div key={playlist.id} className="card p-6 space-y-4 hover:shadow-xl transition-shadow">
                <div className="flex items-center justify-between">
                  <span className="px-3 py-1 bg-primary-100 dark:bg-primary-900/30 text-primary-800 dark:text-primary-200 rounded-full text-sm font-medium">
                    {playlist.platform_name}
                  </span>
                  {playlist.is_highlight && (
                    <span className="px-2 py-1 bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-200 rounded text-xs font-medium">
                      Featured
                    </span>
                  )}
                </div>
                
                <h3 className="text-xl font-semibold text-dark-900 dark:text-white">
                  {playlist.title}
                </h3>
                
                {playlist.description && (
                  <p className="text-dark-600 dark:text-dark-400 text-sm">
                    {playlist.description}
                  </p>
                )}
                
                {playlist.tags && playlist.tags.length > 0 && (
                  <div className="flex flex-wrap gap-2">
                    {playlist.tags.map((tag, index) => (
                      <span 
                        key={index}
                        className="px-2 py-1 bg-dark-100 dark:bg-dark-700 text-dark-700 dark:text-dark-300 rounded text-xs"
                      >
                        {tag}
                      </span>
                    ))}
                  </div>
                )}
                
                <div className="flex items-center justify-between pt-4">
                  {playlist.release_date && (
                    <span className="text-sm text-dark-500 dark:text-dark-400">
                      {new Date(playlist.release_date).toLocaleDateString()}
                    </span>
                  )}
                  <a
                    href={playlist.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="btn btn-primary text-sm"
                  >
                    Listen Now
                  </a>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <div className="space-y-4">
              <svg className="w-16 h-16 mx-auto text-dark-400 dark:text-dark-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3" />
              </svg>
              <h3 className="text-lg font-medium text-dark-900 dark:text-white">
                No playlists found
              </h3>
              <p className="text-dark-600 dark:text-dark-400">
                Try adjusting your filters or check back later for new content.
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default Streaming
