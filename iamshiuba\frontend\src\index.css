@import "tailwindcss/preflight";
@import "tailwindcss/components";
@import "tailwindcss/utilities";

@layer base {
  html {
    font-family: 'Inter', system-ui, sans-serif;
  }
  
  body {
    @apply bg-white dark:bg-dark-900 text-dark-900 dark:text-dark-50;
    @apply transition-colors duration-300;
  }
  
  * {
    @apply border-dark-200 dark:border-dark-700;
  }
}

@layer components {
  .btn {
    @apply px-4 py-2 rounded-lg font-medium transition-all duration-200;
    @apply focus:outline-none focus:ring-2 focus:ring-offset-2;
  }
  
  .btn-primary {
    @apply bg-primary-600 text-white hover:bg-primary-700;
    @apply focus:ring-primary-500;
  }
  
  .btn-secondary {
    @apply bg-dark-200 text-dark-900 hover:bg-dark-300;
    @apply dark:bg-dark-700 dark:text-dark-100 dark:hover:bg-dark-600;
    @apply focus:ring-dark-500;
  }
  
  .btn-danger {
    @apply bg-red-600 text-white hover:bg-red-700;
    @apply focus:ring-red-500;
  }
  
  .input {
    @apply w-full px-3 py-2 border rounded-lg;
    @apply bg-white dark:bg-dark-800;
    @apply border-dark-300 dark:border-dark-600;
    @apply focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent;
    @apply placeholder-dark-400 dark:placeholder-dark-500;
  }
  
  .card {
    @apply bg-white dark:bg-dark-800 rounded-lg shadow-lg;
    @apply border border-dark-200 dark:border-dark-700;
  }
  
  .navbar {
    @apply bg-white/80 dark:bg-dark-900/80 backdrop-blur-md;
    @apply border-b border-dark-200 dark:border-dark-700;
  }
  
  .loading-spinner {
    @apply animate-spin rounded-full border-2 border-dark-300;
    @apply border-t-primary-600 dark:border-dark-600 dark:border-t-primary-400;
  }
}

@layer utilities {
  .text-gradient {
    @apply bg-gradient-to-r from-primary-600 to-purple-600 bg-clip-text text-transparent;
  }
  
  .glass {
    @apply bg-white/10 backdrop-blur-md border border-white/20;
  }
  
  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }
  
  .animate-slide-up {
    animation: slideUp 0.3s ease-out;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  @apply bg-dark-100 dark:bg-dark-800;
}

::-webkit-scrollbar-thumb {
  @apply bg-dark-300 dark:bg-dark-600 rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-dark-400 dark:bg-dark-500;
}

